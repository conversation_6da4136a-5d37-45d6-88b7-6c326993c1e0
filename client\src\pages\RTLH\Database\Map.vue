<template>
  <div id="page-db-map">
    <Map
      width="100vw"
      height="calc(100vh - 110px)"
      :lat="detail.GeoLat"
      :lon="detail.GeoLng"
      :searchbox="false"
      :markers="markers"
      :disabled="true"
      @ready="MapReady"
      @change="MapChange"
      @marker-click="MapMarkerClick"
      @marker-hover="MapMarkerHover"
    />
    <div class="left-panel">
      <div class="detail-panel" style="padding: 8px 12px">
        <input
          v-model="searchNik"
          type="text"
          style="
            width: calc(100% - 30px);
            font-size: 14px;
            outline-color: white;
            padding: 1px;
          "
          placeholder="Cari NIK ..."
        />
        <v-icon>mdi-magnify</v-icon>
      </div>
      <div v-show="showDetail" class="detail-panel">
        <div v-if="detail.KRT_Nama" class="padding">
          <div>
            {{ detail.KRT_Nama }}
          </div>
          <div style="font-size: 12px">
            {{ detail.NIK }}
          </div>
          <div style="font-size: 12px; color: gray">
            {{ detail.Alamat }}
          </div>
          <div style="font-size: 12px; color: gray">
            {{ detail.Kabupaten }}, {{ detail.Kecamatan }},
            {{ detail.Kelurahan }}
          </div>
          <div style="font-size: 12px">
            <b>Bantuan:</b> {{ detail.SumberName }}
          </div>
        </div>
        <div
          v-else-if="searchNik.length == 16"
          class="padding"
          style="font-size: small"
        >
          NIK Tidak Ditemukan
        </div>
        <div v-if="detail.Kabupaten" class="padding">
          <div>
            {{ detail.Kabupaten }}
          </div>
          <div style="font-size: 12px">
            {{ detail.Kecamatan }}{{ detail.Kelurahan ? ',' : '' }}
            {{ detail.Kelurahan }}
          </div>
          <div style="font-size: 12px">
            <b>Jml RTLH:</b> {{ $filters.format(detail.Total) }}
            <br />
            <b>Jml Intervensi:</b> {{ $filters.format(detail.Intervensi) }}
            <br />
            <b>Sisa RTLH:</b> {{ $filters.format(detail.Sisa) }}
          </div>
        </div>
        <div
          style="
            display: flex;
            width: 310px;
            max-height: 300px;
            flex-wrap: wrap;
            overflow: auto;
          "
        >
          <div
            v-for="(item, idx) in photos"
            v-show="detail[item.key]"
            :key="idx"
            style="width: 150px"
          >
            <div style="padding: 5px 8px; font-size: 12px; background: #f3f3f3">
              {{ item.text }}
            </div>
            <div
              class="imgbox"
              :style="{
                'background-image': `url(${detail[item.key]})`,
              }"
            ></div>
          </div>
        </div>
      </div>
      <div v-show="!showDetail" class="detail-panel">
        <div class="padding">
          <Checkbox
            v-for="(layer, idx) in layers"
            :key="idx"
            :text="layer.name"
            :value="layer.visible"
            @click="LayerClick(layer, ...arguments)"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    dbref: String,
    dbparams: Object,
    noRef: [Number, String],
  },
  data: () => ({
    markers: [],
    layers: [],
    detail: {},
    cache: {},
    showDetail: false,
    xbdparams: {},
    searchNik: '',
    viewType: '',
    value: {},
    photos: [
      { key: 'Profile', text: 'Profile' },
      { key: 'RumahDepan', text: 'RUMAH DEPAN' },
      { key: 'RumahSamping', text: 'RUMAH SAMPING' },
      { key: 'Atap0', text: 'ATAP 0%' },
      { key: 'Atap50', text: 'ATAP 50%' },
      { key: 'Atap100', text: 'ATAP 100%' },
      { key: 'Lantai0', text: 'LANTAI 0%' },
      { key: 'Lantai50', text: 'LANTAI 50%' },
      { key: 'Lantai100', text: 'LANTAI 100%' },
      { key: 'Dinding0', text: 'DINDING 0%' },
      { key: 'Dinding50', text: 'DINDING 50%' },
      { key: 'Dinding100', text: 'DINDING 100%' },
    ],
  }),
  watch: {
    noRef(val) {
      this.Populate(val)
    },
    searchNik(val) {
      if (val.length == 16) this.SearchByNIK(val)
    },
  },
  methods: {
    async MapReady() {
      let ret = await this.$api.call(this.dbref, this.xbdparams)
      this.markers = ret.data
    },
    MapChange(map) {
      this.layers = []
      // For Leaflet, we need to iterate through our marker layers
      Object.keys(this.$refs.map?.markerLayers || {}).forEach((layerName) => {
        const layer = this.$refs.map.markerLayers[layerName]
        this.layers.push({
          instance: layer,
          name: layerName.replace('markers-', '').replace('markers', 'default'),
          visible: map.hasLayer(layer),
        })
      })
    },
    async MapMarkerClick(data) {
      let d = JSON.parse(data)

      let ret = await this.$api.call(this.dbref, d)
      this.markers = ret.data
    },
    async MapMarkerHover(data) {
      if (data) {
        let d = JSON.parse(data)
        this.detail = d
        if (d.Kabupaten) {
          if (this.cache[`${d.Kabupaten}-${d.Kelurahan}`]) {
            this.detail = Object.assign(
              this.detail,
              this.cache[`${d.Kabupaten}-${d.Kelurahan}`]
            )
          } else {
            let ret = await this.$api.call('EVO_SelMapInfo', {
              Kabupaten: d.Kabupaten,
              Kecamatan: d.Kecamatan,
              Kelurahan: d.Kelurahan,
            })
            if (ret.data && ret.data.length > 0) {
              this.cache[`${d.Kabupaten}-${d.Kelurahan}`] = ret.data[0]
              this.detail = Object.assign(this.detail, ret.data[0])
            }
            this.showDetail = true
          }
        }
      } else {
        this.showDetail = false
      }
    },
    async SearchByNIK(nik) {
      let ret = await this.$api.call('EVO.SelMapPerson', { NIK: nik })
      if (ret.data.length > 0) this.detail = ret.data[0]
      else this.detail = {}
      // this.markers.push({
      //   icon: 'rgba(255,255,255,0.5)',
      //   iconSrc: '/img/icons/circle.png',
      //   lat: ret.data[0].GeoLat,
      //   lon: ret.data[0].GeoLng,
      //   layer: 'Hasil Pencarian',
      //   noRef: ret.data[0].NoRef,
      //   title: '{"ViewType":"PERSON"}',
      // })
      this.showDetail = true
    },
    LayerClick(layer, visible) {
      layer.visible = visible
      // For Leaflet, add or remove layer from map
      if (visible) {
        this.$refs.map.map.addLayer(layer.instance)
      } else {
        this.$refs.map.map.removeLayer(layer.instance)
      }
    },
    async Populate(val) {
      let ret = await this.$api.call('EVO.SelPersonSummary', { NoUrutRT: val })
      this.detail = ret.data[0]
      this.showDetail = true
    },
  },
}
</script>
<style lang="scss">
#page-db-map {
  position: relative;
  overflow: hidden;
  .left-panel {
    position: absolute;
    width: 310px;
    right: 10px;
    top: 10px;
    .detail-panel {
      background: white;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
      border-radius: 5px;
      overflow: hidden;
      max-height: 600px;
      margin-bottom: 10px;
    }
  }

  .imgbox {
    background: #ddd;
    width: 150px;
    height: 150px;
  }
}
</style>
