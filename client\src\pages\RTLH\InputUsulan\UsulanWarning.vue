<template>
  <v-alert v-if="isMonevNotComplete" type="warning" style="margin-bottom: 10px">
    <v-row style="padding: 5px 0 0 15px">
      Usulan &nbsp;<strong>TIDAK AKAN DIPROSES</strong>&nbsp; lebih lanjut ji<PERSON> & LPJ tahun sebelumnya belum lengkap
    </v-row>
    <v-row style="justify-content: end; padding-bottom: 5px">
      <v-btn
        text
        variant="outlined"
        color="black"
        @click="$router.push('/Main/RTLH/Monev/')"
      >
        BUKA MONEV
      </v-btn>
      <v-btn style="margin-right: 15px" @click="isMonevNotComplete = false">
        OK
      </v-btn>
    </v-row>
  </v-alert>
</template>
<script>
export default {
  props: {
    area: {
      type: Object,
      default: () => {},
    },
  },
  data: () => ({
    isMonevNotComplete: false,
  }),
  watch: {
    'area.Ke<PERSON>rahan'() {
      this.CekMonev()
    },
  },
  methods: {
    async CekMonev() {
      let d = await this.$api.call('PRM_SelMonevComplete', this.area)
      if (d.data.length > 0) this.isMonevNotComplete = true
      else this.isMonevNotComplete = false
    },
  },
}
</script>
