<template>
  <div>
    <div
      v-for="(item, idx) in items"
      :key="idx"
      class="pesan"
      @mouseenter="MouseOver($event)"
      @mouseleave="MouseLeave($event)"
      @click="ItemClick(item)"
    >
      <div v-if="disabled">
        <div class="--title">{{ item.Nama }} . {{ item.NIK }}</div>
        <div class="--subtitle">
          {{ item.Kabupaten }} . {{ item.Kecamatan }} . {{ item.Kelurahan }}
        </div>
      </div>
      <div class="--message">
        {{ item.Pesan }}
      </div>
      <div class="--time">
        <div class="--text">
          {{ item.FullName }} .
          {{ $filters.format(item.CreatedDate, 'fromNow') }}
        </div>
        <v-btn text class="--button" color="red" @click="Delete(item.MessageID)"
          >Delete</v-btn
        >
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    items: Array,
    disabled: <PERSON><PERSON><PERSON>,
  },
  methods: {
    async MouseOver(evt) {
      if (this.disabled) return
      evt.target.querySelector('button').style.display = 'initial'
    },
    async MouseLeave(evt) {
      if (this.disabled) return
      evt.target.querySelector('button').style.display = 'none'
    },
    async Delete(id) {
      this.$emit('delete', id)
    },
    ItemClick(item) {
      this.$emit('click', item)
    },
  },
}
</script>
<style lang="scss">
.pesan {
  font-size: 13px;
  padding: 8px 0;
  border-bottom: 1px solid silver;

  &:hover {
    background: rgba(200, 200, 200, 0.3);
  }
  .--title {
    font-weight: bold;
    flex: 1;
  }
  .--subtitle {
    font-size: 10px;
  }
  .--time {
    font-size: 8px;
    display: flex;
    .--text {
      flex: 1;
      color: gray;
    }
    .v-btn {
      padding: 0 !important;
      height: 17px !important;
      font-size: 10px !important;
      margin-top: -5px;
      display: none;
    }
  }
}
</style>
