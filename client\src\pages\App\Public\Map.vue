<template>
  <v-container style="max-width: 100vw; padding: 0">
    <Header />
    <DBMap dbref="EVO.SelPBDTMap" :disabled="true" />
  </v-container>
</template>

<script>
import Header from '../Header.vue'
import Footer from '../Footer.vue'
import DBMap from '../../RTLH/Database/Map.vue'
export default {
  components: {
    Header,
    DBMap,
    Footer,
  },
  data: () => ({
    forms: {
      username: '',
      password: '',
    },
    showNews: false,
  }),
  mounted() {
    sessionStorage.clear()
  },
  methods: {},
}
</script>
<style lang="scss">
// .theme--light.v-application {
//   background: #f3f3f3;
// }
.login-box {
  width: 300px;
  background: white;
  box-sizing: content-box;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  margin: auto;
  margin-top: 10%;
  text-align: center;
  overflow: hidden;
}
</style>
