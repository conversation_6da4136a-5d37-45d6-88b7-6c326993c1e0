{"name": "client", "version": "0.0.0", "license": "ISC", "scripts": {"start": "vite --host --port 8000", "denorun": "deno run --allow-env --allow-read --allow-sys --allow-ffi --allow-run --allow-write --allow-net npm:vite --host --port 8000", "build": "vite build", "preview": "vite preview --port 4173", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@primeuix/themes": "^1.1.1", "@vue-leaflet/vue-leaflet": "^0.10.1", "axios": "^1.6.7", "chart.js": "2", "exif-js": "^2.3.0", "exifr": "^7.1.3", "floating-vue": "^2.0.0-rc.7", "leaflet": "^1.9.4", "material-design-icons-iconfont": "^5.0.1", "moment": "^2.30.1", "primevue": "^4.3.4", "unplugin-vue-components": "^0.26.0", "v-click-outside": "^3.2.0", "vue": "^3.4.21", "vue-chartjs": "3", "vue-leaflet": "^0.1.0", "vue-router": "^4.3.0", "vue-toast-notification": "^3.1.1", "vue3-toastify": "^0.2.2", "vuetify": "^3.8.5", "vuex": "^4.1.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.0", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-prettier": "^7.0.0", "eslint": "^8.5.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^2.5.1", "sass": "^1.70.0", "terser": "^5.14.2", "vite": "^5.0.0", "vite-plugin-pwa": "^0.13.3", "vite-plugin-vuetify": "^2.1.1"}}