// import Vue from 'vue' // Not needed in Vue 3
import { createVuetify } from 'vuetify' // Import createVuetify
import 'vuetify/styles' // Import Vuetify styles
import { VBtn } from 'vuetify/components' // Import only VBtn component
// import * as directives from 'vuetify/directives' // Import directives

// Vue.use(Vuetify) // Not needed in Vue 3

export default createVuetify({
  components: {
    VBtn,
  },
  // directives,
  // Add any Vuetify 3 options here
})
