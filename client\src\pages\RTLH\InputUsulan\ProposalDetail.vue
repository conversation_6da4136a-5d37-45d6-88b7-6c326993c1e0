<template>
  <Modal
    v-model:show="xshow"
    title="PROPOSAL"
    :show-actions="!disabled"
    width="900px"
    :style="'overflow-x:hidden;'"
    @on-submit="Save"
  >
    <div style="font-size: 14px; width: 600px; max-width: 100vw">
      <div style="width: 100%">
        <div style="padding: 10px; background: #f3f3f3">
          <i class="fa fa-globe"></i>&nbsp;&nbsp;PETA LOKASI
          <span style="color: red">{{
            !forms.GeoLat ? '(Belum Diisi)' : ''
          }}</span>
        </div>
        <div id="MapDiv" style="position: relative; width: 100%">
          <Map
            ref="map"
            v-model:lat="forms.GeoLat"
            v-model:lon="forms.GeoLng"
            style="
              width: 600px;
              max-width: 100vw;
              height: 340px;
              display: block;
            "
          />
        </div>
      </div>
      <div>
        <div style="display: flex; flex-wrap: wrap">
          <Uploader
            v-model:value="forms.PermohonanBansos"
            label="RENCANA KEG."
            accept=".pdf"
          ></Uploader>
          <Uploader
            v-model:value="forms.BAPokmas"
            label="BA. MUSDES"
            accept=".pdf"
          ></Uploader>
          <Uploader
            v-model:value="forms.SKPokmas"
            label="DFTR HADIR"
            accept=".pdf"
          ></Uploader>
          <Uploader
            v-model:value="forms.PaktaIntegritas"
            label="SP KEPDES"
            accept=".pdf"
          ></Uploader>
          <Uploader
            v-model:value="forms.KTP"
            label="KTP"
            accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
          ></Uploader>
          <Uploader
            v-model:value="forms.RumahDepan"
            label="RUMAH DEPAN"
            accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
          ></Uploader>
          <Uploader
            v-model:value="forms.RumahSamping"
            label="RMH SAMPING"
            accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
          ></Uploader>
          <Uploader
            v-if="forms.JenisKloset !== 4"
            v-model:value="forms.Jamban0"
            label="JAMBAN"
            style="margin-top: 0"
            accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
          ></Uploader>
          <div v-else style="width: 150px; height: 150px; text-align: center">
            <v-icon large style="margin-top: 35%">mdi-close-thick</v-icon>
          </div>
          <Uploader
            v-model:value="forms.Atap0"
            label="ATAP 0%"
            accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
          ></Uploader>
          <Uploader
            v-model:value="forms.Lantai0"
            label="LANTAI 0%"
            accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
          ></Uploader>
        </div>
      </div>
      <div
        class="iblock dv-kerusakan"
        style="width: 600px; overflow: hidden; padding: 10px"
      >
        <div style="padding: 0px; font-weight: bold">USULAN PERBAIKAN</div>
        <div style="flex-wrap: wrap; width: 100%" class="usulan-perbaikan">
          <div
            style="padding: 5px; width: 50%; min-width: 280px"
            class="form-inline"
          >
            <Checkbox
              v-model:value="forms.AtapTdkLayak"
              label="Atap"
              style="width: 200px"
              checked-icon="mdi-home-remove"
              checked-color="error"
            />
            <Checkbox
              v-model:value="forms.LantaiTdkLayak"
              label="Lantai"
              style="width: 200px"
              checked-icon="mdi-home-remove"
              checked-color="error"
            />
            <Checkbox
              v-model:value="forms.DindingTdkLayak"
              label="Dinding"
              style="width: 200px"
              checked-icon="mdi-home-remove"
              checked-color="error"
            />
            <Checkbox
              v-model:value="forms.CahayaTdkLayak"
              label="Jendela"
              style="width: 200px"
              checked-icon="mdi-home-remove"
              checked-color="error"
            />
            <!-- <Checkbox
              label="Pintu"
              style="width: 200px"
              checkedIcon="mdi-home-remove"
              v-model:value="forms.CahayaTdkLayak"
            /> -->
            <Checkbox
              v-model:value="forms.JambanTdkLayak"
              label="Km. Mandi/Jamban"
              style="width: 200px"
              checked-icon="mdi-home-remove"
              checked-color="error"
            />
          </div>
          <div
            style="padding: 5px; width: 50%; min-width: 280px"
            class="form-inline"
          >
            <Checkbox
              v-model:value="forms.ListrikTdkLayak"
              label="Listrik"
              style="width: 200px"
              checked-icon="mdi-home-remove"
              checked-color="error"
            />
            <Checkbox
              v-model:value="forms.PondasiTdkLayak"
              label="Pondasi"
              style="width: 200px"
              checked-icon="mdi-home-remove"
              checked-color="error"
            />
            <Checkbox
              v-model:value="forms.SloofTdkLayak"
              label="Sloof"
              style="width: 200px"
              checked-icon="mdi-home-remove"
              checked-color="error"
            />
            <Checkbox
              v-model:value="forms.KolomTdkLayak"
              label="Kolom"
              style="width: 200px"
              checked-icon="mdi-home-remove"
              checked-color="error"
            />
            <Checkbox
              v-model:value="forms.BalokTdkLayak"
              label="Balok"
              style="width: 200px"
              checked-icon="mdi-home-remove"
              checked-color="error"
            />
          </div>
        </div>
      </div>
      <div style="padding: 10px; background: orange; display: flex">
        <div v-if="rab.Swadaya" style="padding: 9px; font-weight: bold">
          Kisaran Swadaya: Rp.
          {{ $filters.format(rab.Swadaya < 0 ? 0 : rab.Swadaya) }}
        </div>
        <div v-else style="padding: 9px; font-weight: bold; color: white">
          RAB BELUM DIMASUKKAN
        </div>
        <v-spacer />
        <v-btn variant="text" color="primary" @click="OpenRAB">
          ESTIMASI RAB
          <v-icon right> mdi-chevron-right </v-icon>
        </v-btn>
      </div>
    </div>
    <RAB
      v-model:show="showRAB"
      :NIK="forms.NIK"
      :tahun="tahun"
      :filters="rabFilters"
    />
  </Modal>
</template>
<script>
import { mapGetters } from 'vuex'
import Map from '../../../components/Forms/Map.vue'
import RAB from './RAB.vue'
export default {
  components: {
    RAB,
    Map,
  },
  props: {
    show: Boolean,
    disabled: Boolean,
    nik: [String, Number],
    tahun: [String, Number],
  },
  data: () => ({
    xshow: false,
    showRAB: false,
    forms: {},
    rab: {},
  }),
  computed: {
    ...mapGetters({
      user: 'getUser',
    }),
    rabFilters() {
      let f = ['E']
      if (this.forms.AtapTdkLayak) f.push('B')
      if (this.forms.LantaiTdkLayak) f.push('C')
      if (this.forms.DindingTdkLayak) f.push('A')
      if (this.forms.JambanTdkLayak) f.push('D')
      // console.log(f)
      return f
    },
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      if (!val) this.forms = {}
      else if (this.xshow) this.populate()
      this.$emit('update:show', val)
    },
  },
  methods: {
    async populate() {
      this.loading = true
      if (this.nik) {
        if (this.$refs.map) this.$refs.map.clearMarkers()
        let { data } = await this.$api.call('PRM.SelPersonProposal', {
          NIK: this.nik,
        })
        // Object.assign(this.forms, data[0])
        this.forms = data[0]

        let rabData = await this.$api.call('PRM.SelRAB', {
          NIK: this.nik,
          Tahun: this.tahun,
        })
        if (rabData.data.length) this.rab = rabData.data[0]
        else this.rab = {}
      } else {
        this.forms = {}
      }
      this.loading = false
    },
    OpenRAB() {
      if (this.rabFilters.length < 2) {
        alert('Pastikan USULAN PERBAIKAN sudah sesuai, sebelum membuat RAB')
      }
      this.showRAB = true
    },
    async Save() {
      this.error = ''
      let ret = await this.$api.call('PRM.SavPersonProposal', {
        ...this.forms,
        GeoLoc: this.forms.GeoLat
          ? this.forms.GeoLat + '|' + this.forms.GeoLng
          : null,
      })
      if (ret.success) {
        this.xshow = false
        this.$emit('save')
      } else this.error = ret.message
    },
  },
}
</script>
<style lang="scss">
.modal-proposal {
  .v-dialog--content {
    padding: 0;
    overflow-x: hidden;
  }
  .usulan-perbaikan {
    display: flex;
  }
}
.is-mobile {
  .modal-proposal {
    .ui-upload {
      width: 50%;
      .imgbox {
        width: 100%;
      }
    }
    .usulan-perbaikan {
      display: block;
    }
  }
}
</style>
