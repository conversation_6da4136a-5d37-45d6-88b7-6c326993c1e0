<template>
  <div
    :id="id"
    class="ui-upload"
    :class="{
      ['mode-' + mode]: true,
      'form-coms': mode == 'form',
    }"
    style="position: relative"
  >
    <div
      v-if="label"
      class="--label"
      :class="{
        ['--lines' + lines]: true,
        'form-label': mode == 'form',
      }"
    >
      <span :style="{ color: labelColor }">{{ label }}</span>
      <v-icon
        v-if="exifData?.latitude"
        v-tooltip="'Ada Geotag'"
        small
        color="success"
        style="margin-left: 5px"
        @click="geoTagIconClicked"
      >
        mdi-map-marker-radius
      </v-icon>
      <v-icon
        v-if="required && !value"
        v-tooltip="'Perlu diupload'"
        small
        color="error"
        style="margin-left: 5px"
      >
        mdi-alert-circle
      </v-icon>
      <v-icon
        v-else-if="aiLoading"
        v-tooltip="'Sedang diverifikasi...'"
        style="margin-left: 5px"
      >
        mdi-head-dots-horizontal
      </v-icon>
      <v-icon
        v-else-if="aiVerified"
        v-tooltip="'Lolos verifikasi sistem'"
        color="success"
        style="margin-left: 5px"
      >
        mdi-head-check
      </v-icon>
      <v-icon
        v-else-if="aiErrors"
        v-tooltip="aiErrors"
        color="warning"
        style="margin-left: 5px"
      >
        mdi-head-alert
      </v-icon>
      <v-icon
        v-else-if="!aiVerified && verify"
        v-tooltip="'Belum diverifikasi sistem'"
        color="primary"
        style="margin-left: 5px"
        @click="aiVerify(value)"
      >
        mdi-head-question
      </v-icon>
    </div>
    <input
      v-show="false"
      :key="resetFile"
      ref="uploader"
      type="file"
      v-bind="$attrs"
      multiple
      @change="previewFiles"
    />
    <slot
      :opener="handleOpen"
      :dropper="handleDrop"
      :drag-over="handleDragOver"
      :drag-leave="handleDragLeave"
      :is-loading="loading"
      :uploaded-file="value"
    >
      <v-icon
        v-if="mode == 'icon'"
        :color="value ? 'primary' : ''"
        @click="handleOpen"
      >
        {{
          loading
            ? 'mdi-loading mdi-spin'
            : value
            ? 'mdi-upload'
            : 'mdi-upload-outline'
        }}
      </v-icon>
      <div v-else-if="mode == 'form'" class="formbox" style="width: 100%">
        <v-btn
          v-show="!value"
          text
          style="width: 100%"
          :loading="loading"
          :disabled="disabled"
          @click="handleOpen"
        >
          <v-icon left>mdi-upload</v-icon>
          UPLOAD
        </v-btn>
        <v-btn
          v-show="value"
          text
          style="width: calc(100% - 50px)"
          class="btn-download"
          color="primary"
          @click="handleItemClick({ text: 'View' })"
        >
          <v-icon left>mdi-download</v-icon>
          {{ downloadText || 'Download' }}
        </v-btn>
        <v-btn
          v-show="value"
          text
          style="
            width: 50px;
            min-width: 50px;
            background: transparent;
            color: red !important;
          "
          :disabled="disabled"
          @click="handleItemClick({ text: 'Clear' })"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </div>
      <div
        v-else
        class="imgbox opener dropbox"
        :style="imgboxStyle"
        @click="handleOpen"
        @drop.prevent="handleDrop"
        @dragover="handleDragOver"
        @dragenter="handleDragOver"
        @dragleave="handleDragLeave"
      ></div>
    </slot>
    <!-- <VueQrcode
      :id="id + `_qrcode`"
      :value="qrcode"
      v-if="showQRCode"
      :style="{
        width: '150px',
        height: '150px',
        position: 'absolute',
        top: '0px',
        left: '0px',
      }"
    /> -->
    <Popover
      :target-id="id"
      group="uploader-ctx"
      class="uploader-ctx"
      on="contextmenu"
    >
      <template>
        <div
          v-for="(item, idx) in opts"
          :key="idx"
          class="ui-dropdown--item popover-close"
          @click="handleItemClick(item)"
        >
          <div class="ui-dropdown--item-inner">
            {{ item.text }}
          </div>
        </div>
      </template>
    </Popover>
  </div>
</template>

<script>
import Popover from '../Forms/Popover.vue'
// import MinifyJpegAsync from '../../api/minifyJpeg'
import copyExif from '../../api/copyExif'
// import EXIF from "exif-js";
// import VueQrcode from 'vue-qrcode'
import exifr from 'exifr'

//import Vue from "vue";

export default {
  components: {
    Popover,
    // VueQrcode,
  },
  props: {
    label: String,
    value: String,
    mode: String,
    downloadText: String,
    lines: {
      type: Number,
      default: 1,
    },
    required: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    verify: {
      type: Object,
    },
  },
  data: () => ({
    files: [],
    id: 'upload',
    uploadedFile: null,
    loading: false,
    aiLoading: false,
    showQRCode: false,
    resetFile: 1,
    clickCount: 0,
    clickTor: null,
    dummyImage: new Image(),
    baseUrl: window.location.origin.replace(':8000', ':8001'),
    alternateBaseUrl: 'https://simperum.disperakim.jatengprov.go.id',
    exifData: null,
  }),
  computed: {
    labelColor() {
      if (this.label?.match(/\*$/) && !this.value) {
        return 'red'
      } else {
        return 'initial'
      }
    },
    qrcode() {
      return (
        window.location.protocol +
        '//' +
        window.location.host.replace(':8000', ':8001') +
        '/Main/App/Extender?clientKey=' +
        window.ClientKey +
        '&cmd=upload'
      )
    },
    opts() {
      if (this.showQRCode) return [{ text: 'Cancel' }]
      else if (this.value)
        return [
          /*{ text: 'Mobile Upload' },*/ { text: 'View' },
          { text: 'Paste' },
          { text: 'Clear' },
        ]
      else if (this.disabled) return [{ text: 'View' }, { text: 'Paste' }]
      else
        return [
          /*{ text: 'Mobile Upload' }*/
          { text: 'Paste' },
          { text: 'Clear' },
        ]
    },
    imgboxStyle() {
      if (this.loading) {
        return {
          'background-image': 'url(/img/icons/loading.gif)',
        }
      } else if (!this.value) {
        return {
          'background-image': 'url(/img/icons/camera.png)',
        }
      } else {
        // let ext = this.value.match(/\.(pdf|doc|docx|ppt|pptx|xls|xlsx)/i)
        let ext = this.value.match(/\.([a-zA-Z0-9]+)$/i)
        if (ext) ext = ext[1].toLowerCase()
        if (
          ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'].includes(ext)
        ) {
          return {
            'background-image': 'url(/img/icons/' + ext + '-icon.png)',
          }
        } else if (['jpg', 'jpeg', 'png'].includes(ext)) {
          return {
            'background-image':
              'url(' +
              this.baseUrl +
              this.value.replace('/ori/', '/small/') +
              ')',
            'background-size': 'cover',
          }
        }
        return {
          'background-image': 'url(' + this.baseUrl + this.value + ')',
          'background-size': 'cover',
        }
      }
    },
    aiErrors() {
      let errors = ''
      if (this.verify && this.value && this.value.match(/\?errors/)) {
        let m = this.value.match(/\?errors=([\w,]+)/)
        if (m) {
          for (let x of m[1].split(',')) {
            errors += this.verify[x].label + ' tidak terbaca dengan jelas\n'
          }
        }
        if (!errors) errors = 'Pengecekan sistem gagal'
      }
      return errors
    },
    aiVerified() {
      if (this.verify && this.value && this.value.match(/\?verified/)) {
        return true
      }
      return false
    },
  },
  watch: {
    value(val) {
      this.exifData = null
      if (val) this.checkExif()
    },
  },
  created() {
    if (!window.uuid) window.uuid = 0
    this.id = 'upload-' + window.uuid++
  },
  methods: {
    handleOpen() {
      this.clickCount++
      if (this.clickCount == 1) {
        this.clickTor = setTimeout(() => {
          this.clickCount = 0
          clearTimeout(this.clickTor)
          if (!this.disabled) this.$refs.uploader.click()
        }, 500)
      } else if (this.clickCount >= 2) {
        this.clickCount = 0
        clearTimeout(this.clickTor)
        this.handleDblClick()
      }
    },
    handleDblClick() {
      this.handleItemClick({ text: 'View' })
    },
    handleDrop(evt) {
      evt.target.classList.remove('dragover')
      evt.preventDefault()
      // console.log('DROP', evt)
      if (evt.dataTransfer.files.length == 1) {
        // console.log(evt.dataTransfer.files[0])
        this.previewFiles({ target: { files: evt.dataTransfer.files } })
      }
      // evt.dataTransfer.files.forEach((file, i) => {
      //   console.log(`… file[${i}].name = ${file.name}`)
      // })
    },
    handleDragOver(evt) {
      evt.target.classList.add('dragover')
      // console.log('DragOver', evt)
      evt.preventDefault()
    },
    handleDragLeave(evt) {
      evt.target.classList.remove('dragover')
      evt.preventDefault()
    },
    geoTagIconClicked() {
      this.$emit('change', {
        value: this.value,
        meta: {
          gps: {
            lat: this.exifData?.latitude,
            lon: this.exifData?.longitude,
          },
        },
      })
    },
    checkExif() {
      this.exifData = null
      //this.dummyImage = new Image()
      this.dummyImage.onload = () => {
        exifr
          .parse(this.dummyImage)
          .then((exif) => {
            if (exif?.latitude && exif?.longitude) {
              this.exifData = exif
            } else {
              this.exifData = null
            }
          })
          .catch(() => {
            console.log('Error reading EXIF')
          })
      }
      this.dummyImage.onerror = () => {
        if (this.baseUrl != this.alternateBaseUrl) {
          this.baseUrl = this.alternateBaseUrl
          this.dummyImage.src = this.alternateBaseUrl + this.value
        }
      }
      this.dummyImage.src = this.$api.url + this.value
    },
    blobToImage(blob) {
      return new Promise((resolve) => {
        const reader = new FileReader(),
          image = new Image()
        image.onload = () => resolve(image)
        reader.onload = ({ target: { result: dataURL } }) =>
          (image.src = dataURL)
        reader.readAsDataURL(blob)
      })
    },
    async resizeFiles(files) {
      let promises = []
      for (let i = 0, f; (f = files[i]); i++) {
        if (f.name.match(/\.(jpg|jpeg)$/i)) {
          const blobOri = await this.blobToImage(f)
          promises.push(
            new Promise((resolve, reject) => {
              const canvas = document.createElement('canvas')
              const ctx = canvas.getContext('2d')

              // Determine the maximum width and height for the canvas
              const maxWidth = 1280
              const maxHeight = 1280

              // Calculate the new dimensions while maintaining the aspect ratio
              let newWidth = blobOri.width
              let newHeight = blobOri.height

              if (newWidth > maxWidth) {
                newHeight = Math.floor(newHeight * (maxWidth / newWidth))
                newWidth = maxWidth
              }

              if (newHeight > maxHeight) {
                newWidth = Math.floor(newWidth * (maxHeight / newHeight))
                newHeight = maxHeight
              }

              canvas.width = newWidth
              canvas.height = newHeight

              ctx.drawImage(blobOri, 0, 0, canvas.width, canvas.height)
              canvas.toBlob(async (blob) => {
                let nf = new File([await copyExif(f, blob)], f.name, {
                  type: f.type,
                })
                resolve(nf)
              }, 'image/jpeg')
            })
          )
        } else {
          promises.push(f)
        }
      }
      return Promise.all(promises)
    },
    async previewFiles(event) {
      if (!event.target.files?.[0]?.name) return
      if (event.target.files[0].name.length > 80) {
        alert('Nama File terlalu panjang')
        this.resetFile++
        return
      }
      const files = await this.resizeFiles(event.target.files)

      // let exifData = exifr.parse(event.target.files[0])
      // console.log(exifData)

      this.$emit('start', event)
      var form = new FormData()
      this.loading = true
      form.append('file', files[0])
      let res = await this.$api.upload(form)
      this.loading = false
      if (res.success) {
        let val = res.data

        this.uploadedFile = val
        this.$emit('update:value', val)
        this.$emit('change', res)

        this.aiVerify(val)
      }
      this.resetFile++
    },
    async aiVerify(val) {
      this.aiLoading = true

      if (this.verify) {
        let vv = await this.$api.post('/api/verify', {
          fileUrl: val.replace(/\/uploads/, 'uploads'),
          verify: this.verify,
        })
        if (!vv.success) {
          if (vv.errors) val += '?errors=' + vv.errors.join(',')
          else if (vv.message) this.$api.notify(vv.message, 'error')
        } else {
          val += '?verified=1'
        }
      }

      this.uploadedFile = val
      this.$emit('update:value', val)
      // this.$emit('change', res)
      this.aiLoading = false
    },
    async handleItemClick(item) {
      if (item.text == 'Mobile Upload') {
        this.showQRCode = true
        if (this.$options.sockets.onmessage)
          delete this.$options.sockets.onmessage
        this.$options.sockets.onmessage = (evt) => {
          let d = JSON.parse(evt.data)
          if (d.clientKey == window.ClientKey && this.showQRCode) {
            this.$emit('update:value', d.value)
            this.$emit('change', d)
            this.resetFile++
            this.showQRCode = false
          }
        }
      } else if (item.text == 'View') {
        if (this.value)
          window.open(
            this.$api.url + this.value.replace(/^tmp\//, '/reports/get/'),
            '_blank'
          )
      } else if (item.text == 'Paste') {
        try {
          const items = await navigator.clipboard.read()
          // Validate clipboard content type and size
          if (items.length > 10) throw new Error('Too many items')

          // Only allow image types
          const allowedTypes = ['image/png', 'image/jpeg', 'image/gif']
          for (const item of items) {
            if (!item.types.some((type) => allowedTypes.includes(type))) {
              throw new Error('Invalid content type')
            }
          }

          // Size validation will happen in subsequent blob handling

          let result
          for (let i = 0, len = items.length; i < len; i++) {
            const item = items[i]
            if (item.types.includes('image/png')) {
              const blob = await item.getType('image/png')
              if (!result) result = []
              result.push(blob)
            }
          }
          if (result) {
            // Do something with the pasted blobs
            // console.log(result)
            const img = new File([result[0]], 'image.png', {
              type: result[0].type,
            })

            this.previewFiles({ target: { files: [img] } })
          }
        } catch (error) {
          console.error('Error pasting image:', error)
          this.$api.notify('Bukan data gambar', 'error')
          // alert('Failed to paste image: ' + error.message)
        }
      } else if (item.text == 'Cancel') {
        if (this.$options.sockets.onmessage)
          delete this.$options.sockets.onmessage
        this.showQRCode = false
      } else if (item.text == 'Clear') {
        this.$emit('update:value', '')
        this.$emit('change', null)
        this.resetFile++
      }
    },
  },
}
</script>
<style lang="scss">
.dragover {
  opacity: 0.5;
}
.ui-upload {
  width: 150px;
  &.mode-icon {
    width: auto !important;
  }
  display: inline-block;
  .--label {
    padding: 10px;
    background: rgb(243, 243, 243);
    width: 100%;
    display: flex;
    font-size: small;

    &.--lines1 {
      white-space: nowrap;
    }
    &.--lines3 {
      height: 78.5px;
    }
  }
  .upload-image {
    width: 200px;
    height: 200px;
    background-color: #f3f3f3;
    background-position: center;
    background-repeat: no-repeat;
  }
  .opener.dropbox {
    background-position: 50%;
  }
  .imgbox {
    width: 150px;
    max-width: 100%;
    height: 150px;
    max-height: 100%;
    background: silver;
    border-right: 1px solid white;
    box-sizing: border-box;
    background-repeat: no-repeat;
    background-position: center;

    &.empty {
      background-image: url('/img/icons/camera.png');
      background-size: initial;
    }
  }
  .ui-popover.uploader-ctx {
    border: 1px solid silver;
    max-height: 200px;
    overflow: auto;
    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.3);
    min-width: 100px;
    .ui-dropdown--item {
      padding: 0 8px;
      cursor: pointer;
      &-inner {
        padding: 5px 0;
        border-bottom: 1px solid #ddd;
        text-align: left;
        font-size: 14px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      &:last-child {
        .ui-dropdown--item-inner {
          border-bottom-color: transparent;
        }
      }
      &:hover {
        background-color: rgba(0, 0, 0, 0.1);
      }
    }
  }
}
</style>
