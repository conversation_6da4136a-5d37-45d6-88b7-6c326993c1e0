<template>
  <div class="form-coms ui-dropdown">
    <div
      v-if="$attrs.label"
      class="form-label"
      :class="{ '--required': hasError }"
    >
      {{ $attrs.label }}
    </div>
    <div
      class="ui-dropdown--button"
      :class="{
        '--empty': value === null || value === undefined,
      }"
      :style="{
        width: width,
      }"
    >
      <PSelect
        v-model="selectedData"
        size="small"
        :options="options"
        :option-label="_textKey"
        :placeholder="placeholder || 'Pilih...'"
        class="w-full"
        :disabled="disabled"
        :filter="options.length > 10"
        @change="handleItemClick"
      />
    </div>
  </div>
</template>

<script>
// import Popover from './Popover.vue'
import PSelect from 'primevue/select'

export default {
  name: 'XSelect',
  components: {
    // Popover,
    PSelect,
  },
  props: {
    value: {
      type: [String, Number, Object],
      default: null,
    },
    dbref: {
      type: String,
      default: null,
    },
    dbparams: {
      type: Object,
      default: () => ({}),
    },
    items: {
      type: Array,
      default: null,
    },
    placeholder: {
      type: String,
      default: null,
    },
    valueKey: {
      type: String,
      default: null,
    },
    textKey: {
      type: String,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    selectFirstOption: {
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '40vh',
    },
    valueAsObject: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:value', 'update:text', 'change'],
  data: () => ({
    options: [],
    text: '-',
    isOpen: false,
    id: 'ddl',
    selectedData: null,
    oldDbparams: null,
  }),
  computed: {
    opts() {
      return this.items || this.options
    },
    _valueKey() {
      return this.valueKey || this.opts?.length
        ? Object.keys(this.opts[0])[0]
        : null
    },
    _textKey() {
      return this.textKey || this.opts?.length
        ? Object.keys(this.opts[0])[1]
        : null
    },
    hasError() {
      return (
        this.$attrs.label &&
        this.$attrs.label.match(/\*$/) &&
        (this.value === null || this.value === undefined || this.value === '')
      )
    },
  },
  watch: {
    value(val) {
      this.setText(val)
    },
    dbparams(val) {
      if (this.oldDbparams != JSON.stringify(val)) this.populate()
      this.oldDbparams = JSON.stringify(val)
    },
  },
  async created() {
    if (!window.uuid) window.uuid = 0
    this.id = 'ddl-' + window.uuid++
    this.populate()
    // if (this.dbref) {
    //   this.populate()
    // } else {
    //   this.setText(this.value)
    // }
  },
  methods: {
    async populate() {
      if (this.items?.length) {
        this.options = this.items
        this.setText(this.value)
        return
      }
      let opts = await this.$api.call(this.dbref, this.dbparams)
      if (opts.data.length) {
        this.options = opts.data
        if (opts.data.length == 1 || this.selectFirstOption) {
          // autoselect
          this.$emit('update:value', opts.data[0][this._valueKey])
        }
        let val = this.value
        this.setText(val)
      }
    },
    setText(val) {
      if (val !== null && val !== undefined) {
        let v = null
        let k = '_'
        // console.log('typeof', typeof val, val)
        if (typeof val == 'string' || typeof val == 'number') v = val
        else if (typeof val == 'object' && Object.keys(val).length == 1) {
          k = Object.keys(val)[0]
          v = val[k]
        } else if (typeof val == 'object' && Object.keys(val).length >= 2) {
          k = Object.keys(val)[0]
          v = val[k]
        } else {
          return
        }

        // console.log('opts.some', v, this.opts.length)
        this.opts.some((e) => {
          // console.log('e', e[this._valueKey], e[k])
          if (e[this._valueKey] == v || e[k] == v) {
            this.text = e[this._textKey]
            this.selectedData = e
            // console.log('text', this.text)
            this.$emit('update:text', this.text)
            if (this.valueAsObject) this.$emit('update:value', e)
            return true
          }
          return false
        })
      } else {
        this.text = this.placeholder || '-'
      }
    },
    handleItemClick(evt) {
      const item = evt.value

      this.text = item[this._textKey]
      if (this.valueAsObject) this.$emit('update:value', item)
      else this.$emit('update:value', item[this._valueKey])
      this.$emit('change', item[this._valueKey], this.text)
    },
    handlePopoverShow($popover) {
      this.isOpen = true
      let $this = document.getElementById(this.id)
      $popover.style.minWidth = $this.offsetWidth + 'px'
    },
    handlePopoverHide() {
      this.isOpen = false
    },
  },
}
</script>
<style lang="scss">
.p-select-list-container {
  font-size: 14px;
}
.p-select-overlay {
  z-index: 10000 !important;
}
.w-full {
  width: 100% !important;
}
.ui-dropdown {
  margin-bottom: 8px;
  color: #333;

  .form-label {
    text-align: left;

    &.--required {
      color: red;
    }
  }

  &--button {
    /*position: relative;*/
    height: 34px;

    &.--empty {
      button {
        color: gray;
      }
    }

    .--wraper {
      position: relative;
      button {
        padding: 4px 5px;
        border: 0;
        background: rgba(200, 200, 200, 0.2);
        border-bottom: 1px solid silver;
        font-size: 14px;
        border-radius: 0;
        text-align: left;
        width: 100%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        padding-right: 20px;

        &:hover,
        &:focus {
          border: 0;
          background: rgba(200, 200, 200, 0.4);
          border-bottom: 1px solid gray;
        }
      }
      .ui-arrow-icon {
        position: absolute;
        right: 0px;
        top: calc(7% + 0.5px);
        &.--open {
          transform: rotate(180deg);
        }
      }
    }
  }
  .ui-popover {
    border: 1px solid silver;
    max-height: 40vh;
    overflow: auto;
    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.3);
    position: fixed;
    .ui-dropdown--item {
      padding: 0 8px;
      cursor: pointer;
      &-inner {
        padding: 5px 0;
        border-bottom: 1px solid #ddd;
        text-align: left;
        font-size: 14px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      &:last-child {
        .ui-dropdown--item-inner {
          border-bottom-color: transparent;
        }
      }
      &:hover {
        background-color: rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.dense {
  .ui-dropdown {
    .ui-dropdown--button button {
      font-size: 14px;
    }
    .ui-popover {
      .ui-dropdown--item {
        &-inner {
          font-size: 14px;
        }
      }
    }
  }
}
</style>
